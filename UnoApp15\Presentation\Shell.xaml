﻿<UserControl x:Class="UnoApp15.Presentation.Shell"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:UnoApp15.Presentation"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:utu="using:Uno.Toolkit.UI"
      mc:Ignorable="d"
      d:DesignHeight="300"
      d:DesignWidth="400">
  <Border Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <utu:ExtendedSplashScreen x:Name="Splash"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                HorizontalContentAlignment="Stretch"
                VerticalContentAlignment="Stretch">
      <utu:ExtendedSplashScreen.LoadingContentTemplate>
        <DataTemplate>
          <Grid>
            <Grid.RowDefinitions>
              <RowDefinition Height="2*" />
              <RowDefinition />
            </Grid.RowDefinitions>

            <ProgressRing IsActive="True"
                  Grid.Row="1"
                  VerticalAlignment="Center"
                  HorizontalAlignment="Center"
                  Height="100"
                  Width="100" />
          </Grid>
        </DataTemplate>
      </utu:ExtendedSplashScreen.LoadingContentTemplate>
    </utu:ExtendedSplashScreen>
  </Border>
</UserControl>
