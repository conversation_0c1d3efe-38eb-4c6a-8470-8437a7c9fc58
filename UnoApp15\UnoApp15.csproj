<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0-desktop</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>UnoApp15</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.UnoApp15</ApplicationId>
    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <!-- Package Publisher -->
    <ApplicationPublisher>bret</ApplicationPublisher>
    <!-- Package Description -->
    <Description>UnoApp15 powered by Uno Platform.</Description>

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Lottie;
      Hosting;
      Toolkit;
      Logging;
      Mvvm;
      Configuration;
      Localization;
      Navigation;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Build.Locator" />
    <PackageReference Include="Microsoft.CodeAnalysis.Workspaces.MSBuild" />
  </ItemGroup>

  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
  </PropertyGroup>

</Project>
