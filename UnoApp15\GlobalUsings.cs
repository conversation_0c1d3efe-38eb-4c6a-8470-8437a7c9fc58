﻿global using System.Collections.Immutable;
global using CommunityToolkit.Mvvm.ComponentModel;
global using CommunityToolkit.Mvvm.Input;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Localization;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using UnoApp15.Models;
global using UnoApp15.Presentation;
global using ApplicationExecutionState = Windows.ApplicationModel.Activation.ApplicationExecutionState;
